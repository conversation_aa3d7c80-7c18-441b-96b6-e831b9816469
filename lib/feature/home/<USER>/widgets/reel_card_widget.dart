import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/feature/home/<USER>/views/place_details_screen.dart';
import 'package:gather_point/feature/reels/presentation/views/single_reel_page.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/utils/sound_manager.dart';

class ReelCardWidget extends StatefulWidget {
  final Map<String, dynamic> item;
  final VoidCallback? onTap; // Custom tap callback

  const ReelCardWidget({
    super.key,
    required this.item,
    this.onTap,
  });

  @override
  State<ReelCardWidget> createState() => _ReelCardWidgetState();
}

class _ReelCardWidgetState extends State<ReelCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _playButtonController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _playButtonAnimation;

  @override
  void initState() {
    super.initState();

    // Pulse animation for card border
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Heartbeat animation for play button
    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _playButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _playButtonController,
      curve: Curves.elasticOut,
    ));

    // Start animations with random delay
    Future.delayed(Duration(milliseconds: 800 + (widget.item.hashCode % 1000)),
        () {
      if (mounted) {
        _startAnimations();
      }
    });
  }

  void _startAnimations() {
    _pulseController.repeat(reverse: true);
    _playButtonController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _playButtonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final image = (widget.item['gallery'] as List?)?.isNotEmpty == true
        ? widget.item['gallery'][0]['image']
        : widget.item['image'] ?? 'https://via.placeholder.com/280x200';
    final title = widget.item['title'] ?? '';

    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _playButtonAnimation]),
      builder: (context, child) {
        return Container(
          width: 280,
          margin: const EdgeInsets.only(right: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              ...context.cardShadow,
              BoxShadow(
                color: context.accentColor
                    .withValues(alpha: 0.2 * _pulseAnimation.value),
                blurRadius: 12 + (8 * _pulseAnimation.value),
                spreadRadius: 2 * _pulseAnimation.value,
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () async {
                // Play click sound
                try {
                  await SoundManager.playClickSound();
                } catch (e) {
                  debugPrint('Click sound error: $e');
                }

                if (context.mounted) {
                  if (widget.onTap != null) {
                    // Use custom callback if provided
                    widget.onTap!();
                  } else {
                    // Navigate to single reel page with the selected reel ID
                    final reelId = widget.item['id'];
                    if (reelId != null) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SingleReelPage(
                            selectedReelId: reelId,
                            showBackButton: true,
                          ),
                        ),
                      );
                    } else {
                      // Fallback to place details screen if no ID
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              PlaceDetailsScreen(placeData: widget.item),
                        ),
                      );
                    }
                  }
                }
              },
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Stack(
                  children: [
                    // Background Image
                    Image.network(
                      image,
                      width: 280,
                      height: 200,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        width: 280,
                        height: 200,
                        color:
                            context.secondaryTextColor.withValues(alpha: 0.1),
                        child: Icon(
                          Icons.image_not_supported_rounded,
                          color: context.secondaryTextColor,
                          size: 48,
                        ),
                      ),
                    ),
                    // Gradient Overlay
                    Container(
                      width: 280,
                      height: 200,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withValues(alpha: 0.7),
                          ],
                        ),
                      ),
                    ),
                    // Content overlay - EXACT MATCH TO ORIGINAL
                    Positioned(
                      bottom: 16,
                      left: 16,
                      right: 16,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (title.isNotEmpty)
                            Text(
                              title,
                              style: AppTextStyles.font16Bold.copyWith(
                                color: Colors.white,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: context.accentColor,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  '${widget.item['price'] ?? 0} ${S.of(context).perNight}',
                                  style: AppTextStyles.font12Bold.copyWith(
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              Transform.scale(
                                scale: _playButtonAnimation.value,
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(
                                        alpha: 0.2 +
                                            (0.1 * _pulseAnimation.value)),
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.white.withValues(
                                            alpha: 0.3 * _pulseAnimation.value),
                                        blurRadius: 8 * _pulseAnimation.value,
                                        spreadRadius: 2 * _pulseAnimation.value,
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.play_arrow,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
